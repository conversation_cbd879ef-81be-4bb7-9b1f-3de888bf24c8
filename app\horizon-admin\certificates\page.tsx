'use client';

import { useState, useEffect } from 'react';
import { getAdminServiceByFirm } from '@/services/multiFirmAdminService';
import { useFirm } from '@/contexts/FirmContext';

export default function HorizonCertificatesPage() {
  const [certificates, setCertificates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentFirm, firmConfig } = useFirm();

  useEffect(() => {
    const fetchCertificates = async () => {
      setIsLoading(true);
      try {
        const adminService = getAdminServiceByFirm(currentFirm);
        const data = await adminService.getCertificates();
        setCertificates(data);
      } catch (error) {
        console.error('Error fetching certificates:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCertificates();
  }, [currentFirm]);

  return (
    <div className="px-6 pb-6">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
            style={{ backgroundColor: firmConfig.colors.primary }}
          >
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">
              {firmConfig.displayName} Certificates
            </h1>
            <p className="text-gray-400">
              View and manage certificates for {firmConfig.displayName}
            </p>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div 
              className="w-12 h-12 border-4 border-t-4 rounded-full animate-spin"
              style={{ 
                borderColor: `${firmConfig.colors.border}`,
                borderTopColor: firmConfig.colors.primary
              }}
            ></div>
            <p className="mt-4" style={{ color: firmConfig.colors.primary }}>
              Loading {firmConfig.displayName} certificates...
            </p>
          </div>
        </div>
      ) : (
        <div 
          className="rounded-xl p-6"
          style={{ 
            backgroundColor: `${firmConfig.colors.background}90`,
            border: `1px solid ${firmConfig.colors.border}`
          }}
        >
          <div className="text-center py-12">
            <div 
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
              style={{ backgroundColor: firmConfig.colors.primary + '20' }}
            >
              <svg className="w-8 h-8" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {firmConfig.displayName} Certificates Management
            </h3>
            <p className="text-gray-400 mb-6">
              Certificates management interface for {firmConfig.displayName} is ready. 
              Found {certificates.length} certificates in the system.
            </p>
            <div className="text-sm text-gray-500">
              Full certificates management features can be implemented here using the existing certificate components with firm-specific styling.
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
