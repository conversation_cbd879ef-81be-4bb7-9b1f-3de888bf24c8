import axios from 'axios';
import { FirmType, FIRM_API_URLS } from './api';
import { Order, User, AdminDashboardSummary, OrderStatus, FailureReason, ContainerAccount } from './adminService';

// Create a secure request function for multi-firm support
const createFirmRequest = async (firmType: FirmType, endpoint: string, options: any = {}) => {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  const apiUrl = `${FIRM_API_URLS[firmType]}/${cleanEndpoint}`;
  
  console.log(`Making ${firmType} request to:`, apiUrl);

  return axios({
    url: apiUrl,
    method: options.method || 'GET',
    data: options.data,
    params: options.params,
    headers: options.headers,
    timeout: 30000,
    withCredentials: false,
  });
};

// Multi-firm API call function
export const multiFirmApiCall = async (firmType: FirmType, url: string, options: any = {}) => {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
    
    const isFormData = options.data instanceof FormData;
    
    const headers = {
      ...(!isFormData && { 'Content-Type': 'application/json' }),
      'Accept': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...(options.headers || {})
    };

    const response = await createFirmRequest(firmType, url, {
      ...options,
      headers
    });

    return response.data;
  } catch (error: any) {
    console.error(`API Error for ${firmType}:`, error);
    
    if (error.response?.status === 401) {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('tokenExpiration');
        localStorage.removeItem('username');
        localStorage.removeItem('user_email');
      }
    }
    
    throw error;
  }
};

// Multi-firm admin service factory
export const createMultiFirmAdminService = (firmType: FirmType) => ({
  // Get all orders
  getAllOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get pending orders
  getPendingOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/pending_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get completed orders
  getCompletedOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/completed_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get failed orders
  getFailedOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/failed_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get passed orders
  getPassedOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/passed_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get stage 2 orders
  getStage2Orders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/stage2_accounts');
      return data.map((stage2Order: any) => ({
        ...stage2Order,
        id: stage2Order.stage2_id || stage2Order.id,
        order_id: stage2Order.order_id,
        username: stage2Order.username,
        email: stage2Order.email,
        challenge_type: stage2Order.challenge_type,
        account_size: stage2Order.account_size,
        platform: stage2Order.platform,
        status: 'stage_2' as OrderStatus,
        created_at: stage2Order.created_at,
        server: stage2Order.server,
        platform_login: stage2Order.platform_login,
        platform_password: stage2Order.platform_password,
        profit_target: stage2Order.profit_target,
        session_id: stage2Order.session_id,
        terminal_id: stage2Order.terminal_id,
        is_stage2: true
      }));
    } catch (error) {
      return [];
    }
  },

  // Get live orders
  getLiveOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/live_accounts');
      return data.map((liveOrder: any) => ({
        ...liveOrder,
        id: liveOrder.live_id || liveOrder.id,
        order_id: liveOrder.order_id,
        username: liveOrder.username,
        email: liveOrder.email,
        challenge_type: liveOrder.challenge_type,
        account_size: liveOrder.account_size,
        platform: liveOrder.platform,
        status: 'live' as OrderStatus,
        created_at: liveOrder.created_at,
        server: liveOrder.server,
        platform_login: liveOrder.platform_login,
        platform_password: liveOrder.platform_password,
        profit_target: liveOrder.profit_target,
        session_id: liveOrder.session_id,
        terminal_id: liveOrder.terminal_id,
        is_live: true
      }));
    } catch (error) {
      return [];
    }
  },

  // Get running orders
  getRunningOrders: async (): Promise<Order[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/running_orders');
      return data.map((runningOrder: any) => ({
        ...runningOrder,
        id: runningOrder.id,
        order_id: runningOrder.order_id,
        username: runningOrder.username,
        email: runningOrder.email,
        challenge_type: runningOrder.challenge_type,
        account_size: runningOrder.account_size,
        platform: runningOrder.platform,
        status: 'running' as OrderStatus,
        created_at: runningOrder.created_at,
        server: runningOrder.server,
        platform_login: runningOrder.platform_login,
        platform_password: runningOrder.platform_password,
        session_id: runningOrder.session_id,
        terminal_id: runningOrder.terminal_id,
        profit_target: runningOrder.profit_target,
        completed: runningOrder.completed,
        passed: runningOrder.passed,
        failed: runningOrder.failed,
        is_active: runningOrder.is_active,
        is_stage2: runningOrder.is_stage2,
        is_live: runningOrder.is_live,
        is_running: true
      }));
    } catch (error) {
      return [];
    }
  },

  // Get all users
  getAllUsers: async (): Promise<User[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'auth/users');
      
      if (data && !Array.isArray(data)) {
        return [data];
      }
      
      if (!data) {
        return [];
      }
      
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get certificates
  getCertificates: async (): Promise<any[]> => {
    try {
      const data = await multiFirmApiCall(firmType, 'order/certificates');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get dashboard summary
  getDashboardSummary: async (): Promise<AdminDashboardSummary | null> => {
    try {
      const [
        allOrders,
        users,
        completedOrders,
        failedOrders,
        passedOrders,
        stage2Orders,
        liveOrders,
        runningOrders,
        certificates
      ] = await Promise.all([
        multiFirmApiCall(firmType, 'order/orders').catch(() => []),
        multiFirmApiCall(firmType, 'auth/users').catch(() => []),
        multiFirmApiCall(firmType, 'order/completed_orders').catch(() => []),
        multiFirmApiCall(firmType, 'order/failed_orders').catch(() => []),
        multiFirmApiCall(firmType, 'order/passed_orders').catch(() => []),
        multiFirmApiCall(firmType, 'order/stage2_accounts').catch(() => []),
        multiFirmApiCall(firmType, 'order/live_accounts').catch(() => []),
        multiFirmApiCall(firmType, 'order/running_orders').catch(() => []),
        multiFirmApiCall(firmType, 'order/certificates').catch(() => [])
      ]);

      const pendingOrders = allOrders.filter((order: any) => 
        !order.completed && !order.failed && !order.passed
      );

      return {
        totalOrders: allOrders.length,
        totalUsers: Array.isArray(users) ? users.length : (users ? 1 : 0),
        orderSummary: {
          pending: pendingOrders.length,
          completed: completedOrders.length,
          failed: failedOrders.length,
          passed: passedOrders.length,
          stage_2: stage2Orders.length,
          live: liveOrders.length,
          running: runningOrders.length,
          certificates: certificates.length
        }
      };
    } catch (error) {
      return null;
    }
  }
});

// Export individual firm services
export const entraAdminService = createMultiFirmAdminService('entra');
export const horizonAdminService = createMultiFirmAdminService('horizon');
export const whalesAdminService = createMultiFirmAdminService('whales');

// Export a function to get service by firm type
export const getAdminServiceByFirm = (firmType: FirmType) => {
  switch (firmType) {
    case 'entra':
      return entraAdminService;
    case 'horizon':
      return horizonAdminService;
    case 'whales':
      return whalesAdminService;
    default:
      return entraAdminService;
  }
};
