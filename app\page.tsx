'use client';

import { useEffect, useState } from 'react';
import HeroSection from '@/components/HeroSection';
import HowItWorksSection from '@/components/HowItWorksSection';
import PricingSection from '@/components/PricingSection';
import NewPricingSection from '@/components/NewPricingSection';
import FaqSection from '@/components/FaqSection';
import WhyUsSection from '@/components/WhyUsSection';
import EidSalePopup from '@/components/EidSalePopup';

// Trust badges component
const TrustBadges = () => (
  <section className="py-16 relative overflow-hidden border-t border-gray-800">
    {/* Enhanced background with luxury patterns */}
    <div className="absolute inset-0 z-0">
      <div className="absolute inset-0 opacity-5 bg-grid-pattern"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/30 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
    </div>
    
    {/* Premium glow effects */}
    <div className="absolute -top-40 -right-40 w-[500px] h-[500px] rounded-full bg-teal-600/5 blur-[100px]"></div>
    <div className="absolute -bottom-40 -left-40 w-[500px] h-[500px] rounded-full bg-teal-600/5 blur-[100px]"></div>
    
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div className="text-center mb-10">
        <div className="inline-flex items-center">
          <div className="h-[1px] w-12 bg-gradient-to-r from-transparent to-teal-400/50"></div>
          <p className="mx-4 text-xl text-gray-200 font-medium tracking-wide">
          Trusted by <span className="text-teal-400 font-bold">10,000+</span> traders worldwide
        </p>
          <div className="h-[1px] w-12 bg-gradient-to-l from-transparent to-teal-400/50"></div>
        </div>
      </div>
      
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 sm:gap-8 lg:gap-10">
        <div className="group relative">
          
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          
          {/* Subtle accent line */}
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          
          {/* Content */}
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">$758K+</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Capital Funded</p>
          </div>
        </div>
        
        <div className="group relative">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">98%</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Payout Rate</p>
          </div>
        </div>
        
        <div className="group relative">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">1.7K+</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Traders Funded</p>
          </div>
        </div>
        
        <div className="group relative">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">150+</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Countries</p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default function Home() {
  const [showEidPopup, setShowEidPopup] = useState(false);

  // Show Eid popup after page loads
  useEffect(() => {
    const timer = setTimeout(() => {
      // Check if user has dismissed the popup permanently
      const isDismissed = localStorage.getItem('eid-popup-dismissed');
      if (isDismissed === 'true') {
        return;
      }

      // Check if user has already seen the popup today
      const lastShown = localStorage.getItem('eid-popup-shown');
      const today = new Date().toDateString();

      if (lastShown !== today) {
        setShowEidPopup(true);
      }
    }, 2000); // Show after 2 seconds

    return () => clearTimeout(timer);
  }, []);

  const handleCloseEidPopup = () => {
    setShowEidPopup(false);
    // Remember that user has seen the popup today
    localStorage.setItem('eid-popup-shown', new Date().toDateString());
  };

  // Smooth scroll effect for anchor links
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isAnchor = target.tagName === 'A' && target.getAttribute('href')?.startsWith('#');
      
      if (isAnchor) {
        e.preventDefault();
        const href = target.getAttribute('href') as string;
        const element = document.querySelector(href);
        if (element) {
          window.scrollTo({
            top: element.getBoundingClientRect().top + window.scrollY - 80,
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleAnchorClick);
    return () => document.removeEventListener('click', handleAnchorClick);
  }, []);

  return (
    <main className="relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200">
      {/* Eid Sale Popup */}
      <EidSalePopup
        isOpen={showEidPopup}
        onClose={handleCloseEidPopup}
      />

      {/* Content */}
      <div className="relative z-10">
        {/* Hero Section */}
        <HeroSection />
        
        {/* Trust Badges */}
        <TrustBadges />
        
        {/* Why Us Section */}
        <WhyUsSection />
        
        {/* How It Works Section */}
        <HowItWorksSection />
        
        {/* Pricing Section */}
        <section id="pricing">
          <NewPricingSection />
        </section>
        
        {/* FAQ Section */}
        <section id="faq">
          <FaqSection />
        </section>
        
        {/* Final CTA Section */}
        <section id="footer" className="py-24 relative overflow-hidden bg-[#030609]">
          {/* Enhanced background elements for consistency */}
          <div className="absolute inset-0 z-0">
            {/* Refined gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
            {/* Enhanced geometric elements */}
            <div className="absolute top-0 left-0 w-full h-full opacity-[0.03]">
              <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <defs>
                  <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#4FD1C5" stopOpacity="0.8" />
                    <stop offset="100%" stopColor="#2D3748" stopOpacity="0.4" />
                  </linearGradient>
                  <pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse">
                    <path d="M 8 0 L 0 0 0 8" fill="none" stroke="url(#grid-gradient)" strokeWidth="0.5" />
                  </pattern>
                </defs>
                <rect x="0" y="0" width="100" height="100" fill="url(#grid)" />
              </svg>
            </div>
            {/* Enhanced glowing orbs */}
            <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] top-1/4 -left-[200px] opacity-20"></div>
            <div className="geometric-shape shape-teal-glow w-[500px] h-[500px] bottom-0 right-0 opacity-20"></div>
          </div>
          
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
            <h2 className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-teal-400">
              Get Funded Today.<br className="hidden sm:block" /> Trade Like a Pro.
            </h2>
            
            <p className="mt-6 text-xl text-gray-300 max-w-3xl mx-auto">
              Start your funded trading journey with us today. Join thousands of traders who have turned their skills into consistent profits.
            </p>
            
            <div className="mt-10">
              <a
                href="/signup"
                className="inline-flex items-center justify-center px-10 py-5 border border-transparent text-lg font-semibold rounded-full text-white bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 shadow-lg shadow-teal-500/20 hover:shadow-xl hover:shadow-teal-500/30 transition-all duration-300 transform hover:-translate-y-1"
              >
                Get Funded
              </a>
            </div>
            
            <div className="mt-6">
              <p className="text-gray-400 text-sm">
                No time limits. No hidden fees. Start within minutes.
              </p>
            </div>
        </div>
      </section>
    </div>
    </main>
  );
} 