'use client';

import { useState, useEffect } from 'react';
import { Order } from '@/services/adminService';
import { getAdminServiceByFirm } from '@/services/multiFirmAdminService';
import { useFirm } from '@/contexts/FirmContext';

export default function HorizonOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentFirm, firmConfig } = useFirm();

  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoading(true);
      try {
        const adminService = getAdminServiceByFirm(currentFirm);
        const data = await adminService.getAllOrders();
        setOrders(data);
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, [currentFirm]);

  return (
    <div className="px-6 pb-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
            style={{ backgroundColor: firmConfig.colors.primary }}
          >
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">
              {firmConfig.displayName} Orders
            </h1>
            <p className="text-gray-400">
              Manage and view all orders for {firmConfig.displayName}
            </p>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div 
              className="w-12 h-12 border-4 border-t-4 rounded-full animate-spin"
              style={{ 
                borderColor: `${firmConfig.colors.border}`,
                borderTopColor: firmConfig.colors.primary
              }}
            ></div>
            <p className="mt-4" style={{ color: firmConfig.colors.primary }}>
              Loading {firmConfig.displayName} orders...
            </p>
          </div>
        </div>
      ) : (
        <div 
          className="rounded-xl p-6"
          style={{ 
            backgroundColor: `${firmConfig.colors.background}90`,
            border: `1px solid ${firmConfig.colors.border}`
          }}
        >
          <div className="text-center py-12">
            <div 
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
              style={{ backgroundColor: firmConfig.colors.primary + '20' }}
            >
              <svg className="w-8 h-8" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {firmConfig.displayName} Orders Management
            </h3>
            <p className="text-gray-400 mb-6">
              Orders management interface for {firmConfig.displayName} is ready. 
              Found {orders.length} orders in the system.
            </p>
            <div className="text-sm text-gray-500">
              Full orders table and management features can be implemented here using the existing OrdersTable component with firm-specific styling.
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
