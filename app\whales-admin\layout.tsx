'use client';

import { useState, useEffect } from 'react';
import { FirmProvider } from '@/contexts/FirmContext';
import FirmAdminSidebar from '@/components/FirmAdminSidebar';
import { Inter } from 'next/font/google';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function WhalesAdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Update CSS variable for sidebar width
  useEffect(() => {
    document.documentElement.style.setProperty(
      '--sidebar-width',
      isCollapsed ? '5rem' : '16rem'
    );
  }, [isCollapsed]);

  return (
    <FirmProvider initialFirm="whales">
      <div className={`${inter.className} min-h-screen bg-gradient-to-br from-[#0A0F1A] via-[#0F1629] to-[#050A10] relative overflow-hidden`}>
        {/* Background Effects */}
        <div className="fixed inset-0 z-0">
          {/* Grid pattern */}
          <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-[0.02]"></div>
          
          {/* Geometric shapes */}
          <div className="geometric-shape shape-blue-glow w-[800px] h-[800px] -top-[400px] -left-[400px]"></div>
          <div className="geometric-shape shape-blue-glow w-[600px] h-[600px] -bottom-[200px] -right-[200px]"></div>
          <div className="geometric-shape shape-blue-glow w-[500px] h-[500px] top-[30%] -right-[250px] opacity-[0.07]"></div>
        </div>

        {/* Sidebar */}
        <FirmAdminSidebar onCollapse={(collapsed) => setIsCollapsed(collapsed)} />

        {/* Main content area with margin that matches sidebar width */}
        <div
          className="flex-1 min-h-screen overflow-y-auto transition-all duration-300 pt-6"
          style={{ marginLeft: 'var(--sidebar-width, 16rem)' }}
        >
          {children}
        </div>
      </div>
    </FirmProvider>
  );
}
