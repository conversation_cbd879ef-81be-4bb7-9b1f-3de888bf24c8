'use client';

import { useFirm } from '@/contexts/FirmContext';

interface FirmStatCardProps {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
  change?: number;
  changeLabel?: string;
  className?: string;
}

export default function FirmStatCard({ 
  title, 
  value, 
  icon, 
  change, 
  changeLabel,
  className = ''
}: FirmStatCardProps) {
  const { firmConfig } = useFirm();

  return (
    <div 
      className={`relative overflow-hidden rounded-xl p-6 backdrop-blur-xl ${className}`}
      style={{ 
        backgroundColor: `${firmConfig.colors.background}90`,
        border: `1px solid ${firmConfig.colors.border}`
      }}
      onMouseEnter={(e) => {
        if (className.includes('cursor-pointer')) {
          e.currentTarget.style.borderColor = firmConfig.colors.borderHover.replace(/rgba\([^)]+\)/, firmConfig.colors.primary + '66');
        }
      }}
      onMouseLeave={(e) => {
        if (className.includes('cursor-pointer')) {
          e.currentTarget.style.borderColor = firmConfig.colors.border;
        }
      }}
    >
      <div className="flex justify-between">
        <div>
          <p className="text-sm text-gray-400">{title}</p>
          <p className="text-2xl font-semibold mt-2 text-white">{value}</p>
          
          {(change !== undefined || changeLabel) && (
            <div className="flex items-center mt-2">
              {change !== undefined && (
                <span className={`text-xs font-medium ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {change >= 0 ? '+' : ''}{change}%
                </span>
              )}
              {changeLabel && (
                <span className="text-xs text-gray-400 ml-1">
                  {changeLabel}
                </span>
              )}
            </div>
          )}
        </div>
        
        {icon && (
          <div className="flex items-center justify-center w-12 h-12 rounded-lg" style={{ backgroundColor: firmConfig.colors.primary + '20' }}>
            {icon}
          </div>
        )}
      </div>

      {/* Subtle glow effect for interactive cards */}
      {className.includes('cursor-pointer') && (
        <div 
          className="absolute inset-0 opacity-0 hover:opacity-10 transition-opacity duration-300 pointer-events-none"
          style={{ backgroundColor: firmConfig.colors.primary }}
        ></div>
      )}
    </div>
  );
}
