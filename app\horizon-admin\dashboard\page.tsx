'use client';

import { useState, useEffect } from 'react';
import { AdminDashboardSummary } from '@/services/adminService';
import { getAdminServiceByFirm } from '@/services/multiFirmAdminService';
import { useFirm } from '@/contexts/FirmContext';
import FirmDashboardSummary from '@/components/admin/FirmDashboardSummary';

export default function HorizonDashboardPage() {
  const [summary, setSummary] = useState<AdminDashboardSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { currentFirm, firmConfig } = useFirm();

  useEffect(() => {
    const fetchSummary = async () => {
      setIsLoading(true);
      try {
        const adminService = getAdminServiceByFirm(currentFirm);
        const data = await adminService.getDashboardSummary();
        setSummary(data);
      } catch (error) {
        console.error('Error fetching dashboard summary:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [currentFirm]);

  return (
    <div className="px-6 pb-6">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
            style={{ backgroundColor: firmConfig.colors.primary }}
          >
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
            </svg>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">
              {firmConfig.displayName} Dashboard
            </h1>
            <p className="text-gray-400">
              Overview of all orders and users for {firmConfig.displayName}
            </p>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div 
              className="w-12 h-12 border-4 border-t-4 rounded-full animate-spin"
              style={{ 
                borderColor: `${firmConfig.colors.border}`,
                borderTopColor: firmConfig.colors.primary
              }}
            ></div>
            <p className="mt-4" style={{ color: firmConfig.colors.primary }}>
              Loading {firmConfig.displayName} dashboard data...
            </p>
          </div>
        </div>
      ) : summary ? (
        <FirmDashboardSummary summary={summary} />
      ) : (
        <div 
          className="rounded-lg p-4"
          style={{ 
            backgroundColor: firmConfig.colors.error + '10',
            border: `1px solid ${firmConfig.colors.error}30`,
            color: firmConfig.colors.error
          }}
        >
          Error loading {firmConfig.displayName} dashboard data. Please try again later.
        </div>
      )}
    </div>
  );
}
