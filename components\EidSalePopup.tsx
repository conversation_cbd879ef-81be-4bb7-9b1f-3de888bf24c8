'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface SalePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

// Account pricing with special discounts
const pricingOffers = [
  {
    size: '$1,000',
    originalPrice: 12.5,
    salePrice: 10,
    savings: 2.5,
    discount: '20%',
    popular: false
  },
  {
    size: '$3,000',
    originalPrice: 16.25,
    salePrice: 13,
    savings: 3.25,
    discount: '20%',
    popular: false
  },
  {
    size: '$5,000',
    originalPrice: 27.5,
    salePrice: 22,
    savings: 5.5,
    discount: '20%',
    popular: true
  },
  {
    size: '$10,000',
    originalPrice: 43.75,
    salePrice: 35,
    savings: 8.75,
    discount: '20%',
    popular: false
  },
  {
    size: '$25,000',
    originalPrice: 86.25,
    salePrice: 69,
    savings: 17.25,
    discount: '20%',
    popular: false
  },
  {
    size: '$50,000',
    originalPrice: 200,
    salePrice: 60,
    savings: 140,
    discount: '70%',
    popular: false
  },
  {
    size: '$100,000',
    originalPrice: 283.33,
    salePrice: 85,
    savings: 198.33,
    discount: '70%',
    popular: false
  },
  {
    size: '$200,000',
    originalPrice: 500,
    salePrice: 150,
    savings: 350,
    discount: '70%',
    popular: false
  }
];

const SalePopup: React.FC<SalePopupProps> = ({ isOpen, onClose }) => {
  const [selectedOffer, setSelectedOffer] = useState(pricingOffers[2]); // Default to $5,000
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Countdown timer for limited time offer
  useEffect(() => {
    const saleEndDate = new Date();
    saleEndDate.setDate(saleEndDate.getDate() + 7);

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = saleEndDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay with blur */}
        <div
          className="fixed inset-0 transition-opacity bg-black/90 backdrop-blur-sm animate-fadeIn"
          onClick={onClose}
        />

        {/* Modal content */}
        <div className="inline-block align-bottom bg-[#0A1018] rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full border border-white/5 animate-slideUp">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-white/5 hover:bg-white/10 text-white/60 hover:text-white/90 focus:outline-none transition-all duration-200"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Header */}
          <div className="relative px-6 pt-8 pb-6">
            <div className="absolute inset-0 bg-gradient-to-b from-teal-500/10 to-transparent pointer-events-none"></div>
            <div className="relative z-10 text-center">
              <div className="inline-block mb-3">
                <div className="bg-teal-500/10 backdrop-blur-sm rounded-full px-4 py-1 border border-teal-500/20">
                  <span className="text-teal-300 text-sm font-medium tracking-wide">FLASH SALE • 60% OFF</span>
                </div>
              </div>
              <h2 className="text-2xl font-bold text-white mb-2 tracking-tight">
                Premium Trading Challenge
              </h2>
              <p className="text-base text-white/60">
                Limited Time Exclusive Offer
              </p>
            </div>
          </div>

          {/* Countdown Timer */}
          <div className="px-6 pb-6">
            <div className="bg-gradient-to-r from-slate-800/50 to-slate-800/30 rounded-xl p-4 border border-white/5">
              <div className="grid grid-cols-4 gap-3">
                {Object.entries(timeLeft).map(([unit, value]) => (
                  <div key={unit} className="text-center">
                    <div className="bg-black/50 rounded-lg px-2 py-3 border border-white/5">
                      <div className="text-2xl font-bold text-white mb-1">{value}</div>
                      <div className="text-xs text-white/40 uppercase tracking-wider">{unit}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-6 pb-6">
            {/* Account Size Selection */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-white/60 mb-3 text-center tracking-wide">
                Select Your Account Size
              </h3>
              <div className="grid grid-cols-4 gap-2">
                {pricingOffers.map((offer, index) => (
                  <div
                    key={index}
                    onClick={() => setSelectedOffer(offer)}
                    className={`relative cursor-pointer group`}
                  >
                    <div className={`p-3 text-center rounded-xl transition-all duration-300 border ${
                      selectedOffer.size === offer.size
                        ? 'bg-gradient-to-b from-teal-500/20 to-teal-500/5 border-teal-500/30 shadow-lg shadow-teal-500/10'
                        : 'bg-white/5 hover:bg-white/10 border-transparent hover:border-white/10'
                    }`}>
                      {offer.popular && (
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-teal-500 text-white text-[10px] px-2 py-0.5 rounded-full">
                          POPULAR
                        </div>
                      )}
                      <div className="font-medium text-sm text-white mb-1">
                        {offer.size.replace('$', '').replace(',', '')}K
                      </div>
                      <div className={`text-sm font-bold ${selectedOffer.size === offer.size ? 'text-teal-400' : 'text-white/80'}`}>
                        ${offer.salePrice}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Offer Details */}
            <div className="bg-gradient-to-b from-slate-800/50 to-slate-800/30 rounded-xl p-5 border border-white/5 mb-6">
              <div className="text-center">
                <h4 className="text-xl font-bold text-white mb-4">
                  {selectedOffer.size} Account
                </h4>
                <div className="flex justify-center items-center space-x-4 mb-3">
                  <div className="text-base text-white/40 line-through font-medium">${selectedOffer.originalPrice}</div>
                  <div className="px-2 py-1 bg-teal-500/20 rounded-md border border-teal-500/30">
                    <span className="text-teal-400 font-bold">SAVE {selectedOffer.discount}</span>
                  </div>
                  <div className="text-2xl font-bold text-white">${selectedOffer.salePrice}</div>
                </div>
                <div className="text-white/60 text-sm">
                  Total Savings: <span className="text-teal-400 font-semibold">${selectedOffer.savings}</span>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="bg-gradient-to-b from-slate-800/50 to-slate-800/30 rounded-xl p-4 border border-white/5 mb-6">
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2 text-sm">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-white/80">2-Phase Evaluation</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-white/80">90% Profit Share</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-white/80">Pro Trading Tools</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-white/80">Instant Access</span>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex gap-3">
              <Link
                href={`/place-order?type=twoStep&size=${selectedOffer.size.replace('$', '').replace(',', '')}&special_offer=true`}
                className="flex-1 px-6 py-3.5 bg-gradient-to-r from-teal-500 to-teal-400 hover:from-teal-400 hover:to-teal-300 text-white font-semibold rounded-xl shadow-lg shadow-teal-500/25 hover:shadow-teal-500/40 transition-all duration-300 text-center text-sm tracking-wide"
                onClick={onClose}
              >
                Get Started Now
              </Link>
              <button
                onClick={onClose}
                className="px-6 py-3.5 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white font-medium rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 text-sm"
              >
                Maybe Later
              </button>
            </div>

            {/* Footer */}
            <div className="mt-6 text-center">
              <div className="flex justify-center items-center gap-6 text-white/40 text-xs mb-3">
                <div className="flex items-center gap-1.5">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <span>Secure Payment</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>Instant Access</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <span>24/7 Support</span>
                </div>
              </div>
              <button
                onClick={() => {
                  localStorage.setItem('sale-popup-dismissed', 'true');
                  onClose();
                }}
                className="text-white/40 hover:text-white/60 text-xs transition-colors"
              >
                Don't show this offer again
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalePopup;
