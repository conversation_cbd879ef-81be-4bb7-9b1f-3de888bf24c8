'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';

// Account type configuration
type AccountTypeKey = 'instant' | 'oneStep' | 'twoStep';
type AccountSizeKey = 1000 | 3000 | 5000 | 10000 | 25000 | 50000 | 100000 | 200000;

interface AccountTypeInfo {
  name: string;
  description: string;
  phases: number;
  phasesLabel: string;
  targetMultiplier: number;
  maxCapital: number;
}

// Account types
const accountTypes: Record<AccountTypeKey, AccountTypeInfo> = {
  instant: {
    name: 'Instant',
    description: 'Start trading immediately with a funded account after completion of purchase.',
    phases: 0,
    phasesLabel: 'No evaluation',
    targetMultiplier: 10,
    maxCapital: 50000
  },
  oneStep: {
    name: '1-Step',
    description: 'Complete one evaluation to prove your trading skills before receiving a funded account.',
    phases: 1,
    phasesLabel: 'One-phase evaluation',
    targetMultiplier: 8,
    maxCapital: 100000
  },
  twoStep: {
    name: '2-Step',
    description: 'Complete a two-phase evaluation process before receiving a funded account.',
    phases: 2,
    phasesLabel: 'Two-phase evaluation',
    targetMultiplier: 6,
    maxCapital: 200000
  }
};

// Account sizes
const accountSizes = [
  { id: 1, value: 1000, label: "1k", discountPercent: 20 },
  { id: 2, value: 3000, label: "3k", discountPercent: 20 },
  { id: 3, value: 5000, label: "5k", discountPercent: 20 },
  { id: 4, value: 10000, label: "10k", discountPercent: 20 },
  { id: 5, value: 25000, label: "25k", discountPercent: 20 },
  { id: 6, value: 50000, label: "50k", discountPercent: 60 },
  { id: 7, value: 100000, label: "100k", discountPercent: 60 },
  { id: 8, value: 200000, label: "200k", discountPercent: 60 },
];

// Pricing structure
const pricingStructure: Record<AccountTypeKey, Record<AccountSizeKey, { real: number, discounted: number }>> = {
  instant: {
    1000: { real: 61.25, discounted: 49 },
    3000: { real: 143.75, discounted: 115 },
    5000: { real: 218.75, discounted: 175 },
    10000: { real: 416.25, discounted: 333 },
    25000: { real: 856.25, discounted: 685 },
    50000: { real: 2450, discounted: 735 },
    100000: { real: 4166.67, discounted: 1250 },
    200000: { real: 0, discounted: 0 }
  },
  oneStep: {
    1000: { real: 12.5, discounted: 10 },
    3000: { real: 16.25, discounted: 13 },
    5000: { real: 27.5, discounted: 22 },
    10000: { real: 43.75, discounted: 35 },
    25000: { real: 86.25, discounted: 69 },
    50000: { real: 200, discounted: 60 },
    100000: { real: 283.33, discounted: 85 },
    200000: { real: 500, discounted: 150 }
  },
  twoStep: {
    1000: { real: 10, discounted: 8 },
    3000: { real: 12.5, discounted: 10 },
    5000: { real: 21.25, discounted: 17 },
    10000: { real: 32.5, discounted: 26 },
    25000: { real: 72.5, discounted: 58 },
    50000: { real: 140, discounted: 42 },
    100000: { real: 216.67, discounted: 65 },
    200000: { real: 350, discounted: 105 }
  }
};

// Price calculation based on account size and type
const calculatePrice = (accountType: AccountTypeKey, accountSize: AccountSizeKey): number => {
  // Ensure the account type and size exist in our pricing structure
  if (pricingStructure[accountType] && pricingStructure[accountType][accountSize]) {
    return pricingStructure[accountType][accountSize].discounted;
  }
  // Default fallback
  return 0;
};

const NewPricingSection = () => {
  const [accountType, setAccountType] = useState<AccountTypeKey>('instant');
  const [accountSize, setAccountSize] = useState<number>(5000);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  // Update account size when switching account types to ensure valid selection
  useEffect(() => {
    // No longer need to reset 1k and 3k sizes for Instant account type
    // Only need to reset account size if the current selection is no longer valid
    if (pricingStructure[accountType][accountSize as AccountSizeKey].discounted === 0) {
      setAccountSize(5000); // Default to 5k if current selection is invalid
    }
  }, [accountType, accountSize]);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // Calculate price and savings
  const realPrice = pricingStructure[accountType][accountSize as AccountSizeKey].real;
  const discountedPrice = pricingStructure[accountType][accountSize as AccountSizeKey].discounted;
  const savings = realPrice - discountedPrice;

  return (
    <section 
      id="pricing" 
      ref={sectionRef}
      className={`py-24 relative bg-[#030609] ${isVisible ? 'animate-fadeIn' : 'opacity-0'}`}
    >
      {/* Enhanced background elements for consistency */}
      <div className="absolute inset-0 z-0">
        {/* Refined gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        {/* Enhanced geometric elements */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#4FD1C5" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#2D3748" stopOpacity="0.4" />
              </linearGradient>
              <pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse">
                <path d="M 8 0 L 0 0 0 8" fill="none" stroke="url(#grid-gradient)" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect x="0" y="0" width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        {/* Enhanced glowing orbs */}
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>
      {/* Main Content */}
      <div className="container mx-auto px-4 relative z-10">
        {/* Section heading with more premium styling */}
        <div className="text-center mb-12 md:mb-20">
          <div className="inline-block relative mb-2">
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 w-[50px] h-[50px] opacity-20">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-teal-400">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14.5 8.5C14.5 9.88071 13.3807 11 12 11C10.6193 11 9.5 9.88071 9.5 8.5C9.5 7.11929 10.6193 6 12 6C13.3807 6 14.5 7.11929 14.5 8.5Z" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M6 19V18C6 15.7909 7.79086 14 10 14H14C16.2091 14 18 15.7909 18 18V19" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3 className="text-teal-400 text-sm uppercase tracking-[0.3em] font-semibold">FXentra Trading Solutions</h3>
          </div>
          <h2 className="text-3xl md:text-6xl font-bold text-white mb-6 md:mb-8">
            <span className="bg-gradient-to-r from-teal-400 via-emerald-300 to-teal-500 text-transparent bg-clip-text">FXentra</span> Trading Challenges
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-teal-500 to-emerald-400 mx-auto mb-6 md:mb-8"></div>
          <p className="text-gray-300 max-w-2xl mx-auto text-base md:text-lg leading-relaxed px-4">
            Select your FXentra account type and size that aligns with your trading strategy and goals.
            <span className="block text-teal-400 mt-2 font-medium">One-time payment, exclusive FXentra benefits.</span>
          </p>
        </div>

        {/* Trading Challenge Details */}
        <div className="max-w-6xl mx-auto">
          <div className="bg-dark-lighter/30 rounded-2xl border border-gray-800/50 shadow-xl backdrop-blur-sm overflow-hidden">
            {/* Account Type Tabs - Mobile Responsive */}
            <div className="flex justify-center bg-dark/60 p-3 md:p-6">
              <div className="bg-dark/70 rounded-xl p-1 md:p-1.5 flex flex-col md:flex-row border border-gray-700/50 shadow-lg w-full md:w-auto">
                <button 
                  onClick={() => setAccountType('instant')}
                  className={`px-4 md:px-8 py-3 md:py-4 rounded-lg transition-all duration-300 font-medium text-sm md:text-base ${
                    accountType === 'instant' 
                      ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20' 
                      : 'text-gray-400 hover:text-white hover:bg-dark-light'
                  }`}
                >
                  Instant
                </button>
                <button 
                  onClick={() => setAccountType('oneStep')}
                  className={`px-4 md:px-8 py-3 md:py-4 rounded-lg transition-all duration-300 font-medium text-sm md:text-base ${
                    accountType === 'oneStep' 
                      ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20' 
                      : 'text-gray-400 hover:text-white hover:bg-dark-light'
                  }`}
                >
                  1-Step
                </button>
                <button 
                  onClick={() => setAccountType('twoStep')}
                  className={`px-4 md:px-8 py-3 md:py-4 rounded-lg transition-all duration-300 font-medium text-sm md:text-base ${
                    accountType === 'twoStep' 
                      ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20' 
                      : 'text-gray-400 hover:text-white hover:bg-dark-light'
                  }`}
                >
                  2-Step
                </button>
              </div>
            </div>

            <div className="text-center mt-4 md:mt-6 mb-4 md:mb-6 px-4">
              <p className="text-gray-300 max-w-2xl mx-auto text-base md:text-lg">
                {accountTypes[accountType].description}
              </p>
              <p className="text-gray-400 mt-2 md:mt-3 text-sm md:text-base">
                <span className="text-teal-400 font-medium">{accountTypes[accountType].phasesLabel}</span> {accountTypes[accountType].phases > 0 ? `• ${accountTypes[accountType].phases} evaluation challenge${accountTypes[accountType].phases > 1 ? 's' : ''}` : ''}
              </p>
            </div>
            
            {/* Account Size Selector - Mobile Responsive */}
            <div className="grid grid-cols-4 sm:grid-cols-4 md:grid-cols-8 gap-2 md:gap-3 p-4 md:p-8 px-4 md:px-8">
              {accountSizes
                .filter(size => (accountType !== 'instant' || size.value !== 200000) && 
                               (pricingStructure[accountType][size.value as AccountSizeKey].discounted > 0))
                .map((size) => (
                <div 
                  key={size.id}
                  onClick={() => setAccountSize(size.value)}
                  className={`relative cursor-pointer p-2 text-center rounded-xl transition-all duration-300 border ${
                    accountSize === size.value 
                      ? 'bg-gradient-to-b from-teal-600/80 to-teal-700/80 text-white border-teal-400/30 shadow-lg shadow-teal-500/20 transform scale-105' 
                      : 'bg-dark/80 hover:bg-dark-light/80 text-gray-300 border-gray-800/50 hover:border-gray-700/50'
                  }`}
                >
                  {size.discountPercent > 0 && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-[8px] md:text-[10px] font-bold py-0.5 px-1.5 rounded-full transform rotate-3 shadow-lg">
                      {size.discountPercent}% OFF
                    </div>
                  )}
                  <div className="font-bold text-base md:text-lg">{size.label}</div>
                </div>
              ))}
            </div>
            
            {/* Trading Details - Mobile Responsive Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 p-4 md:p-6 pt-0">
              {/* Left Column - Trading Rules */}
              <div className="col-span-1 lg:col-span-2 bg-dark/40 rounded-xl p-4 md:p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-6 md:gap-y-8 gap-x-4 md:gap-x-12">
                  {/* Profit Target Phase 1 - Only show for oneStep and twoStep */}
                  {accountType !== 'instant' && (
                    <div className="flex items-center">
                      <div className="text-teal-400 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10" />
                          <polyline points="8 12 12 16 16 12" />
                          <line x1="12" y1="8" x2="12" y2="16" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Target</div>
                        <div className="flex justify-between items-center">
                          <div className="text-xs text-teal-300 font-semibold">PHASE 1</div>
                          <div className="text-white font-bold text-xl">10%</div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Profit Target Phase 2 - Only show for twoStep */}
                  {accountType === 'twoStep' && (
                    <div className="flex items-center">
                      <div className="text-teal-400 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10" />
                          <polyline points="8 12 12 16 16 12" />
                          <line x1="12" y1="8" x2="12" y2="16" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Target</div>
                        <div className="flex justify-between items-center">
                          <div className="text-xs text-teal-300 font-semibold">PHASE 2</div>
                          <div className="text-white font-bold text-xl">5%</div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Max Daily Loss */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M3 3v18h18" />
                        <path d="M19 9l-5 5-4-4-3 3" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Max Daily Loss</div>
                      <div className="text-white font-bold text-xl">
                        {accountType === 'instant' ? '2.5%' : '4%'}
                      </div>
                    </div>
                  </div>
                  
                  {/* Max Total Loss */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M3 3v18h18" />
                        <path d="M19 9l-5 5-4-4-3 3" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Max Total Loss</div>
                      <div className="text-white font-bold text-xl">
                        {accountType === 'instant' ? '5%' : accountType === 'oneStep' ? '8%' : '10%'}
                      </div>
                    </div>
                  </div>
                  
                  {/* Drawdown Type */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Drawdown Type</div>
                      <div className="text-white font-bold text-xl">Equity Based</div>
                    </div>
                  </div>
                  
                  {/* Profit Split */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <line x1="8" y1="12" x2="16" y2="12" />
                        <line x1="12" y1="16" x2="12" y2="8" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Split</div>
                      <div className="text-white font-bold text-xl">
                        {accountType === 'twoStep' ? '80%' : '90%'}
                      </div>
                    </div>
                  </div>
                  
                  {/* Payout Frequency */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                        <line x1="16" y1="2" x2="16" y2="6" />
                        <line x1="8" y1="2" x2="8" y2="6" />
                        <line x1="3" y1="10" x2="21" y2="10" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Payout Frequency</div>
                      <div className="text-white font-bold text-xl">Bi-Weekly</div>
                    </div>
                  </div>

                  {/* Consistency Rule */}
                  {(accountType === 'instant' || accountType === 'oneStep') && (
                    <div className="flex items-center">
                      <div className="text-teal-400 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Consistency Rule</div>
                        <div className="text-white font-bold text-xl">
                          {accountType === 'instant' ? '30%' : '25%'}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* News Trading */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h12a2 2 0 0 0 2-2V8l-6-6z" />
                        <path d="M14 3v5h5M16 13H8M16 17H8M10 9H8" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">News Trading</div>
                      <div className="text-white font-bold text-xl">
                        {accountType === 'instant' ? 'Not Allowed' : 'Allowed'}
                      </div>
                    </div>
                  </div>

                  {/* Weekend Holding */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                        <line x1="16" y1="2" x2="16" y2="6" />
                        <line x1="8" y1="2" x2="8" y2="6" />
                        <line x1="3" y1="10" x2="21" y2="10" />
                        <path d="M8 14h.01M12 14h.01M16 14h.01M8 18h.01M12 18h.01M16 18h.01" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Weekend Holding</div>
                      <div className="text-white font-bold text-xl">Not Allowed</div>
                    </div>
                  </div>

                  {/* Minimum Trading Days - Only show for oneStep and twoStep */}
                  {accountType !== 'instant' && (
                    <div className="flex items-center">
                      <div className="text-teal-400 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                          <line x1="16" y1="2" x2="16" y2="6" />
                          <line x1="8" y1="2" x2="8" y2="6" />
                          <line x1="3" y1="10" x2="21" y2="10" />
                          <line x1="8" y1="14" x2="8" y2="14" />
                          <line x1="12" y1="14" x2="12" y2="14" />
                          <line x1="16" y1="14" x2="16" y2="14" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Minimum Trading Days</div>
                        <div className="text-white font-bold text-xl">5 Days</div>
                      </div>
                    </div>
                  )}

                  {/* Overnight Holding */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Overnight Holding</div>
                      <div className="text-white font-bold text-xl">Allowed</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Right Column - Price Details - Mobile Responsive */}
              <div className="bg-gradient-to-b from-dark/60 to-dark/40 backdrop-blur-sm rounded-xl p-6 md:p-8 flex flex-col border border-gray-800/50 shadow-xl">
                <div className="mb-auto">
                  <div className="text-center mb-6 md:mb-8">
                    <div className="text-gray-400 text-sm uppercase tracking-wider">Virtual Capital</div>
                    <div className="text-3xl md:text-4xl font-bold text-white my-2 md:my-3">${accountSize.toLocaleString()}</div>
                    <div className="inline-block px-3 md:px-4 py-1 md:py-1.5 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-xs md:text-sm font-bold rounded-full shadow-lg">
                      {accountSize <= 25000 ? "20%" : "60%"} OFF
                    </div>
                  </div>
                  
                  <div className="mb-6 md:mb-8 text-center">
                    <div className="text-sm text-gray-400 uppercase tracking-wider mb-2">Real Price</div>
                    <div className="flex items-center justify-center">
                      <div className="text-xl md:text-2xl text-gray-500 line-through mr-3">${realPrice}</div>
                      <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-teal-400 to-emerald-400 text-transparent bg-clip-text">${discountedPrice}</div>
                    </div>
                    <div className="mt-2 md:mt-3 inline-flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5 text-teal-400 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-300 text-xs md:text-sm font-medium">Save ${savings}</span>
                    </div>
                  </div>
                </div>
                
                {/* CTA Button - Mobile Responsive */}
                <Link 
                  href={`/dashboard/place-order?type=${accountType}&size=${accountSize}`}
                  className="w-full py-4 md:py-5 px-4 md:px-6 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold rounded-xl shadow-lg shadow-teal-500/30 hover:shadow-teal-500/50 transition-all duration-300 transform hover:translate-y-[-2px] text-center border border-teal-400/20 text-sm md:text-base"
                >
                  Start Now
                </Link>
                
                {/* Crypto Payment Methods - Mobile Responsive */}
                <div className="mt-4 md:mt-5 text-center">
                  <div className="text-gray-400 text-xs md:text-sm uppercase tracking-wide mb-2 md:mb-3">We Accept</div>
                  <div className="flex justify-center items-center gap-3 md:gap-4">
                    {/* USDT */}
                    <div className="flex flex-col items-center">
                      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-teal-300">
                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" fill="none" strokeWidth="1.5"/>
                        <path d="M12 6V10M12 14V10M7 10H17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                      </svg>
                      <span className="text-xs mt-1 font-medium text-teal-300">USDT</span>
                    </div>
                    
                    {/* BNB */}
                    <div className="flex flex-col items-center">
                      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-yellow-500">
                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" fill="none" strokeWidth="1.5"/>
                        <path d="M12 7L16 11L12 15L8 11L12 7Z" fill="currentColor"/>
                        <path d="M12 16L14 14L16 16L12 20L8 16L10 14L12 16Z" fill="currentColor"/>
                        <path d="M12 8L14 10L12 12L10 10L12 8Z" fill="currentColor"/>
                      </svg>
                      <span className="text-xs mt-1 font-medium text-yellow-500">BNB</span>
                    </div>
                    
                    {/* BTC */}
                    <div className="flex flex-col items-center">
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-orange-500">
                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5"/>
                        <path d="M7.5 10.5H16.5M7.5 13.5H16.5M12 18V6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                      </svg>
                      <span className="text-xs mt-1 font-medium text-orange-500">BTC</span>
                    </div>
                    
                    {/* ETH */}
                    <div className="flex flex-col items-center">
                      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-blue-400">
                        <path d="M11.944 17.97L4.58 13.62 11.943 24l7.37-10.38-7.372 4.35h.003zM12.056 0L4.69 12.223l7.365 4.354 7.365-4.35L12.056 0z" fill="currentColor"/>
                      </svg>
                      <span className="text-xs mt-1 font-medium text-blue-400">ETH</span>
                    </div>
                  </div>
                </div>
                
                {/* Promotion Box - Mobile Responsive */}
                <div className="mt-6 md:mt-8 bg-gradient-to-r from-teal-900/80 via-teal-800/70 to-teal-900/80 p-4 md:p-5 rounded-lg border border-teal-700/50 shadow-inner">
                  <div className="flex items-center">
                    <span className="text-teal-300 mr-2 md:mr-3 text-lg md:text-xl">✦</span>
                    <div>
                      <div className="text-teal-100 text-sm md:text-base font-semibold">FXentra Exclusive</div>
                      <div className="text-teal-400 font-bold text-lg md:text-xl">PREMIUM</div>
                      <div className="text-teal-100 text-xs md:text-sm">TRADING EXPERIENCE</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewPricingSection;