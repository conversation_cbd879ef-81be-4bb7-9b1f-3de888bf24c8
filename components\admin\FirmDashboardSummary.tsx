'use client';

import Link from 'next/link';
import { AdminDashboardSummary } from '@/services/adminService';
import { useFirm } from '@/contexts/FirmContext';
import FirmStatCard from './FirmStatCard';

interface FirmDashboardSummaryProps {
  summary: AdminDashboardSummary;
}

export default function FirmDashboardSummary({ summary }: FirmDashboardSummaryProps) {
  const { currentFirm, firmConfig } = useFirm();

  // Get the base path for the current firm
  const getBasePath = () => {
    switch (currentFirm) {
      case 'horizon':
        return '/horizon-admin';
      case 'whales':
        return '/whales-admin';
      case 'entra':
        return '/entra-admin';
      default:
        return '/entra-admin';
    }
  };

  const basePath = getBasePath();

  return (
    <>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <FirmStatCard
          title="Total Orders"
          value={summary?.totalOrders || 0}
          icon={
            <svg className="w-8 h-8" style={{ color: firmConfig.colors.primaryLight }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          }
        />
        <FirmStatCard
          title="Total Users"
          value={summary?.totalUsers || 0}
          icon={
            <svg className="w-8 h-8" style={{ color: firmConfig.colors.primaryLight }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          }
        />
        <FirmStatCard
          title="Completed Orders"
          value={summary?.orderSummary.completed || 0}
          icon={
            <svg className="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
        <FirmStatCard
          title="Pending Orders"
          value={summary?.orderSummary.pending || 0}
          icon={
            <svg className="w-8 h-8 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
      </div>

      {/* Order Status Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Link href={`${basePath}/orders/stage-2`} className="block">
          <FirmStatCard
            title="Stage 2 Orders"
            value={summary?.orderSummary.stage_2 || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href={`${basePath}/orders/live`} className="block">
          <FirmStatCard
            title="Live Orders"
            value={summary?.orderSummary.live || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href={`${basePath}/orders/running`} className="block">
          <FirmStatCard
            title="Running Orders"
            value={summary?.orderSummary.running || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href={`${basePath}/orders/failed`} className="block">
          <FirmStatCard
            title="Failed Orders"
            value={summary?.orderSummary.failed || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
            icon={
              <svg className="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
        </Link>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link href={`${basePath}/certificates`} className="block">
          <FirmStatCard
            title="Certificates"
            value={summary?.orderSummary.certificates || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
            icon={
              <svg className="w-8 h-8" style={{ color: firmConfig.colors.primaryLight }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            }
          />
        </Link>
        <Link href={`${basePath}/users`} className="block">
          <FirmStatCard
            title="Verified Users"
            value={summary?.totalUsers || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href={`${basePath}/orders/passed`} className="block">
          <FirmStatCard
            title="Passed Orders"
            value={summary?.orderSummary.passed || 0}
            className="hover:scale-105 transition-all duration-300 cursor-pointer"
            icon={
              <svg className="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
        </Link>
        <div className="relative overflow-hidden rounded-xl p-6" style={{ 
          backgroundColor: `${firmConfig.colors.background}90`,
          border: `1px solid ${firmConfig.colors.border}`
        }}>
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-gray-400">Success Rate</p>
              <p className="text-2xl font-semibold mt-2 text-white">
                {summary?.totalOrders > 0 
                  ? Math.round(((summary.orderSummary.completed + summary.orderSummary.passed) / summary.totalOrders) * 100)
                  : 0
                }%
              </p>
            </div>
            <div className="flex items-center justify-center w-12 h-12 rounded-lg" style={{ backgroundColor: firmConfig.colors.primary + '20' }}>
              <svg className="w-6 h-6" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
