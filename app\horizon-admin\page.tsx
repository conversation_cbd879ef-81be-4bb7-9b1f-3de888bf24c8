'use client';

import { useState, useEffect } from 'react';
import { AdminDashboardSummary } from '@/services/adminService';
import { getAdminServiceByFirm } from '@/services/multiFirmAdminService';
import { useFirm } from '@/contexts/FirmContext';
import FirmDashboardSummary from '@/components/admin/FirmDashboardSummary';
import Link from 'next/link';

export default function HorizonAdminPage() {
  const [summary, setSummary] = useState<AdminDashboardSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { currentFirm, firmConfig } = useFirm();

  useEffect(() => {
    const fetchSummary = async () => {
      setIsLoading(true);
      try {
        const adminService = getAdminServiceByFirm(currentFirm);
        const data = await adminService.getDashboardSummary();
        setSummary(data);
      } catch (error) {
        console.error('Error fetching dashboard summary:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [currentFirm]);

  return (
    <div className="px-6 pb-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
            style={{ backgroundColor: firmConfig.colors.primary }}
          >
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">
              {firmConfig.displayName} Admin Portal
            </h1>
            <p className="text-gray-400">
              Manage orders, users, and trading accounts for {firmConfig.displayName}
            </p>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div 
              className="w-12 h-12 border-4 border-t-4 rounded-full animate-spin"
              style={{ 
                borderColor: `${firmConfig.colors.border}`,
                borderTopColor: firmConfig.colors.primary
              }}
            ></div>
            <p className="mt-4" style={{ color: firmConfig.colors.primary }}>
              Loading {firmConfig.displayName} dashboard data...
            </p>
          </div>
        </div>
      ) : summary ? (
        <>
          {/* Dashboard Summary */}
          <FirmDashboardSummary summary={summary} />

          {/* Quick Access Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
            <Link
              href="/horizon-admin/orders"
              className="group relative overflow-hidden rounded-xl p-6 transition-all duration-300 hover:scale-105"
              style={{ 
                backgroundColor: `${firmConfig.colors.background}90`,
                border: `1px solid ${firmConfig.colors.border}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.borderHover.replace('rgba(234, 88, 12, 0.4)', firmConfig.colors.primary + '66');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.border;
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Orders</h3>
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: firmConfig.colors.primary + '20' }}
                >
                  <svg className="w-5 h-5" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-white">{summary.totalOrders}</span>
                <span className="text-sm text-gray-400">total</span>
              </div>
            </Link>

            <Link
              href="/horizon-admin/containers"
              className="group relative overflow-hidden rounded-xl p-6 transition-all duration-300 hover:scale-105"
              style={{ 
                backgroundColor: `${firmConfig.colors.background}90`,
                border: `1px solid ${firmConfig.colors.border}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.borderHover.replace('rgba(234, 88, 12, 0.4)', firmConfig.colors.primary + '66');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.border;
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Containers</h3>
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: firmConfig.colors.primary + '20' }}
                >
                  <svg className="w-5 h-5" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-white">6</span>
                <span className="text-sm text-gray-400">accounts</span>
              </div>
            </Link>

            <Link
              href="/horizon-admin/users"
              className="group relative overflow-hidden rounded-xl p-6 transition-all duration-300 hover:scale-105"
              style={{ 
                backgroundColor: `${firmConfig.colors.background}90`,
                border: `1px solid ${firmConfig.colors.border}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.borderHover.replace('rgba(234, 88, 12, 0.4)', firmConfig.colors.primary + '66');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.border;
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Users</h3>
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: firmConfig.colors.primary + '20' }}
                >
                  <svg className="w-5 h-5" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-white">{summary.totalUsers}</span>
                <span className="text-sm text-gray-400">registered</span>
              </div>
            </Link>

            <Link
              href="/horizon-admin/certificates"
              className="group relative overflow-hidden rounded-xl p-6 transition-all duration-300 hover:scale-105"
              style={{ 
                backgroundColor: `${firmConfig.colors.background}90`,
                border: `1px solid ${firmConfig.colors.border}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.borderHover.replace('rgba(234, 88, 12, 0.4)', firmConfig.colors.primary + '66');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = firmConfig.colors.border;
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Certificates</h3>
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: firmConfig.colors.primary + '20' }}
                >
                  <svg className="w-5 h-5" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
              <div className="flex items-baseline gap-2">
                <span className="text-2xl font-bold text-white">{summary.orderSummary.certificates}</span>
                <span className="text-sm text-gray-400">issued</span>
              </div>
            </Link>
          </div>
        </>
      ) : (
        <div 
          className="rounded-lg p-4"
          style={{ 
            backgroundColor: firmConfig.colors.error + '10',
            border: `1px solid ${firmConfig.colors.error}30`,
            color: firmConfig.colors.error
          }}
        >
          Error loading {firmConfig.displayName} dashboard data. Please try again later.
        </div>
      )}
    </div>
  );
}
