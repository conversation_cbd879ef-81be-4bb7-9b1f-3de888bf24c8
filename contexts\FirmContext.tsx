'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { FirmType, FIRM_API_URLS } from '@/services/api';

// Firm configuration with colors and branding
export const FIRM_CONFIG = {
  entra: {
    name: '<PERSON><PERSON><PERSON>',
    displayName: 'Entra',
    primaryColor: 'teal',
    colors: {
      primary: '#14B8A6', // teal-500
      primaryLight: '#2DD4BF', // teal-400
      primaryDark: '#0F766E', // teal-600
      accent: '#5EEAD4', // teal-300
      background: '#0F1A2E',
      backgroundLight: '#1E293B',
      border: 'rgba(20, 184, 166, 0.2)', // teal-500/20
      borderHover: 'rgba(20, 184, 166, 0.4)', // teal-500/40
      text: '#FFFFFF',
      textSecondary: '#94A3B8',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    logo: '/images/fxentra-logo.png',
    apiUrl: FIRM_API_URLS.entra
  },
  horizon: {
    name: 'Horizon',
    displayName: 'Horizon',
    primaryColor: 'orange',
    colors: {
      primary: '#EA580C', // orange-600
      primaryLight: '#FB923C', // orange-400
      primaryDark: '#C2410C', // orange-700
      accent: '#FDBA74', // orange-300
      background: '#1A0F0A',
      backgroundLight: '#2D1B0F',
      border: 'rgba(234, 88, 12, 0.2)', // orange-600/20
      borderHover: 'rgba(234, 88, 12, 0.4)', // orange-600/40
      text: '#FFFFFF',
      textSecondary: '#94A3B8',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    logo: '/images/horizon-logo.png',
    apiUrl: FIRM_API_URLS.horizon
  },
  whales: {
    name: 'Whales',
    displayName: 'Whales',
    primaryColor: 'blue',
    colors: {
      primary: '#2563EB', // blue-600
      primaryLight: '#60A5FA', // blue-400
      primaryDark: '#1D4ED8', // blue-700
      accent: '#93C5FD', // blue-300
      background: '#0A0F1A',
      backgroundLight: '#0F1629',
      border: 'rgba(37, 99, 235, 0.2)', // blue-600/20
      borderHover: 'rgba(37, 99, 235, 0.4)', // blue-600/40
      text: '#FFFFFF',
      textSecondary: '#94A3B8',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    logo: '/images/whales-logo.png',
    apiUrl: FIRM_API_URLS.whales
  }
} as const;

export type FirmConfig = typeof FIRM_CONFIG[FirmType];

interface FirmContextType {
  currentFirm: FirmType;
  firmConfig: FirmConfig;
  setCurrentFirm: (firm: FirmType) => void;
  getFirmUrl: (endpoint: string) => string;
}

const FirmContext = createContext<FirmContextType | undefined>(undefined);

interface FirmProviderProps {
  children: React.ReactNode;
  initialFirm?: FirmType;
}

export function FirmProvider({ children, initialFirm = 'entra' }: FirmProviderProps) {
  const [currentFirm, setCurrentFirm] = useState<FirmType>(initialFirm);

  // Get firm configuration
  const firmConfig = FIRM_CONFIG[currentFirm];

  // Function to get full API URL for the current firm
  const getFirmUrl = (endpoint: string): string => {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${firmConfig.apiUrl}/${cleanEndpoint}`;
  };

  // Update CSS variables when firm changes
  useEffect(() => {
    const root = document.documentElement;
    const colors = firmConfig.colors;
    
    root.style.setProperty('--firm-primary', colors.primary);
    root.style.setProperty('--firm-primary-light', colors.primaryLight);
    root.style.setProperty('--firm-primary-dark', colors.primaryDark);
    root.style.setProperty('--firm-accent', colors.accent);
    root.style.setProperty('--firm-background', colors.background);
    root.style.setProperty('--firm-background-light', colors.backgroundLight);
    root.style.setProperty('--firm-border', colors.border);
    root.style.setProperty('--firm-border-hover', colors.borderHover);
    root.style.setProperty('--firm-text', colors.text);
    root.style.setProperty('--firm-text-secondary', colors.textSecondary);
  }, [firmConfig]);

  const value: FirmContextType = {
    currentFirm,
    firmConfig,
    setCurrentFirm,
    getFirmUrl
  };

  return (
    <FirmContext.Provider value={value}>
      {children}
    </FirmContext.Provider>
  );
}

export function useFirm() {
  const context = useContext(FirmContext);
  if (context === undefined) {
    throw new Error('useFirm must be used within a FirmProvider');
  }
  return context;
}

// Helper function to get firm-specific Tailwind classes
export function getFirmClasses(firm: FirmType) {
  const config = FIRM_CONFIG[firm];
  const colorMap = {
    teal: {
      primary: 'teal-500',
      primaryLight: 'teal-400',
      primaryDark: 'teal-600',
      accent: 'teal-300',
      bg: 'bg-teal-500',
      text: 'text-teal-400',
      border: 'border-teal-500/20',
      borderHover: 'hover:border-teal-500/40',
      button: 'bg-teal-600 hover:bg-teal-700',
      gradient: 'from-teal-500 to-teal-600'
    },
    orange: {
      primary: 'orange-600',
      primaryLight: 'orange-400',
      primaryDark: 'orange-700',
      accent: 'orange-300',
      bg: 'bg-orange-600',
      text: 'text-orange-400',
      border: 'border-orange-600/20',
      borderHover: 'hover:border-orange-600/40',
      button: 'bg-orange-600 hover:bg-orange-700',
      gradient: 'from-orange-500 to-orange-600'
    },
    blue: {
      primary: 'blue-600',
      primaryLight: 'blue-400',
      primaryDark: 'blue-700',
      accent: 'blue-300',
      bg: 'bg-blue-600',
      text: 'text-blue-400',
      border: 'border-blue-600/20',
      borderHover: 'hover:border-blue-600/40',
      button: 'bg-blue-600 hover:bg-blue-700',
      gradient: 'from-blue-500 to-blue-600'
    }
  };

  return colorMap[config.primaryColor as keyof typeof colorMap];
}
