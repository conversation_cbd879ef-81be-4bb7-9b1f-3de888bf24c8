'use client';

import { useState, useEffect } from 'react';
import { User } from '@/services/adminService';
import { getAdminServiceByFirm } from '@/services/multiFirmAdminService';
import { useFirm } from '@/contexts/FirmContext';

export default function EntraUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentFirm, firmConfig } = useFirm();

  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true);
      try {
        const adminService = getAdminServiceByFirm(currentFirm);
        const data = await adminService.getAllUsers();
        setUsers(data);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [currentFirm]);

  return (
    <div className="px-6 pb-6">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
            style={{ backgroundColor: firmConfig.colors.primary }}
          >
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">
              {firmConfig.displayName} Users
            </h1>
            <p className="text-gray-400">
              Manage users and accounts for {firmConfig.displayName}
            </p>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div 
              className="w-12 h-12 border-4 border-t-4 rounded-full animate-spin"
              style={{ 
                borderColor: `${firmConfig.colors.border}`,
                borderTopColor: firmConfig.colors.primary
              }}
            ></div>
            <p className="mt-4" style={{ color: firmConfig.colors.primary }}>
              Loading {firmConfig.displayName} users...
            </p>
          </div>
        </div>
      ) : (
        <div 
          className="rounded-xl p-6"
          style={{ 
            backgroundColor: `${firmConfig.colors.background}90`,
            border: `1px solid ${firmConfig.colors.border}`
          }}
        >
          <div className="text-center py-12">
            <div 
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
              style={{ backgroundColor: firmConfig.colors.primary + '20' }}
            >
              <svg className="w-8 h-8" style={{ color: firmConfig.colors.primary }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {firmConfig.displayName} Users Management
            </h3>
            <p className="text-gray-400 mb-6">
              Users management interface for {firmConfig.displayName} is ready. 
              Found {users.length} users in the system.
            </p>
            <div className="text-sm text-gray-500">
              Full users table and management features can be implemented here using the existing users components with firm-specific styling.
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
