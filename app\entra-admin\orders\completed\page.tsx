'use client';

import { useState, useEffect } from 'react';
import { Order } from '@/services/adminService';
import { getAdminServiceByFirm } from '@/services/multiFirmAdminService';
import { useFirm } from '@/contexts/FirmContext';
import OrdersTable from '@/components/admin/OrdersTable';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import EditOrderModal from '@/components/admin/EditOrderModal';

export default function EntraCompletedOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const { currentFirm, firmConfig } = useFirm();

  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoading(true);
      try {
        const adminService = getAdminServiceByFirm(currentFirm);
        const data = await adminService.getCompletedOrders();
        setOrders(data);
      } catch (error) {
        // Error handling without logging
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, [currentFirm]);

  const handleOrderUpdated = (updatedOrder: Order) => {
    setOrders(prevOrders =>
      prevOrders.map(order => order.id === updatedOrder.id ? updatedOrder : order)
    );
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsEditModalOpen(true);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">
          {firmConfig.displayName} Completed Orders
        </h1>
        <p className="text-gray-400">
          View and manage completed orders for {firmConfig.displayName}
        </p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div 
              className="w-12 h-12 border-4 border-t-4 rounded-full animate-spin"
              style={{ 
                borderColor: `${firmConfig.colors.border}`,
                borderTopColor: firmConfig.colors.primary
              }}
            ></div>
            <p className="mt-4" style={{ color: firmConfig.colors.primary }}>
              Loading {firmConfig.displayName} completed orders...
            </p>
          </div>
        </div>
      ) : (
        <OrdersTable
          orders={orders}
          title="Completed Orders"
          onOrderUpdated={handleOrderUpdated}
          onViewOrder={handleViewOrder}
          onEditOrder={handleEditOrder}
        />
      )}

      <ViewOrderModal
        order={selectedOrder}
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        onEdit={handleEditOrder}
      />

      <EditOrderModal
        order={selectedOrder}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onOrderUpdated={handleOrderUpdated}
      />
    </div>
  );
}
