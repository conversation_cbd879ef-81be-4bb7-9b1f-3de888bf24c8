'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { useFirm, getFirmClasses } from '@/contexts/FirmContext';

interface FirmAdminSidebarProps {
  onCollapse: (collapsed: boolean) => void;
}

export default function FirmAdminSidebar({ onCollapse }: FirmAdminSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const { currentFirm, firmConfig } = useFirm();
  const firmClasses = getFirmClasses(currentFirm);

  // Get the base path for the current firm
  const getBasePath = () => {
    switch (currentFirm) {
      case 'horizon':
        return '/horizon-admin';
      case 'whales':
        return '/whales-admin';
      case 'entra':
        return '/entra-admin';
      default:
        return '/entra-admin';
    }
  };

  const basePath = getBasePath();

  const menuItems = [
    {
      name: 'Dashboard',
      href: basePath,
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
      ),
    },
    {
      name: 'Orders',
      href: `${basePath}/orders`,
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
    },
    {
      name: 'Users',
      href: `${basePath}/users`,
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      ),
    },
    {
      name: 'Containers',
      href: `${basePath}/containers`,
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
    },
    {
      name: 'Certificates',
      href: `${basePath}/certificates`,
      icon: (
        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
    },
  ];

  const handleCollapse = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    onCollapse(newCollapsed);
  };

  const isActive = (href: string) => {
    if (href === basePath) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <aside
      className={`fixed top-0 left-0 h-screen text-white transition-all duration-300 z-20 ${
        isCollapsed ? 'w-20' : 'w-64'
      }`}
      style={{ backgroundColor: firmConfig.colors.background }}
    >
      {/* Logo and collapse button */}
      <div 
        className="flex items-center justify-between h-16 px-4"
        style={{ borderBottom: `1px solid ${firmConfig.colors.border}` }}
      >
        {!isCollapsed ? (
          <div className="flex items-center">
            <div className="flex items-center">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center mr-3"
                style={{ backgroundColor: firmConfig.colors.primary }}
              >
                <span className="text-white font-bold text-lg">
                  {firmConfig.displayName.charAt(0)}
                </span>
              </div>
              <div>
                <span className="text-lg font-bold text-white">{firmConfig.displayName}</span>
                <span className="ml-2 text-xs font-medium text-gray-300">Admin</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full flex justify-center">
            <div 
              className="w-8 h-8 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: firmConfig.colors.primary }}
            >
              <span className="text-white font-bold text-lg">
                {firmConfig.displayName.charAt(0)}
              </span>
            </div>
          </div>
        )}
        
        <button
          onClick={handleCollapse}
          className="p-1.5 rounded-lg transition-colors duration-200"
          style={{ 
            backgroundColor: 'transparent',
            color: firmConfig.colors.primaryLight
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = firmConfig.colors.border;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <svg
            className={`w-5 h-5 transition-transform duration-300 ${
              isCollapsed ? 'rotate-180' : ''
            }`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
            />
          </svg>
        </button>
      </div>

      {/* Navigation */}
      <nav className="mt-6 px-3 h-[calc(100vh-8rem)] overflow-y-auto no-scrollbar">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.name}>
              <Link
                href={item.href}
                className={`
                  flex items-center px-3 py-2.5 rounded-lg transition-all duration-200 group
                  ${isActive(item.href) 
                    ? 'text-white shadow-lg' 
                    : 'text-gray-300 hover:text-white'
                  }
                `}
                style={{
                  backgroundColor: isActive(item.href) 
                    ? firmConfig.colors.primary 
                    : 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isActive(item.href)) {
                    e.currentTarget.style.backgroundColor = firmConfig.colors.border;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive(item.href)) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <span className={`${isCollapsed ? 'mx-auto' : 'mr-3'}`}>
                  {item.icon}
                </span>
                {!isCollapsed && (
                  <span className="font-medium">{item.name}</span>
                )}
              </Link>
            </li>
          ))}
        </ul>

        {/* Back to Portals */}
        <div className="mt-8 pt-4" style={{ borderTop: `1px solid ${firmConfig.colors.border}` }}>
          <Link
            href="/portals"
            className="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200 text-gray-400 hover:text-white"
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = firmConfig.colors.border;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <span className={`${isCollapsed ? 'mx-auto' : 'mr-3'}`}>
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </span>
            {!isCollapsed && (
              <span className="font-medium">Back to Portals</span>
            )}
          </Link>
        </div>
      </nav>
    </aside>
  );
}
