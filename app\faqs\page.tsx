'use client';

import { useState, useEffect, useRef } from 'react';

// FAQ data
const faqs = [
  {
    id: 1,
    question: 'What is FXentra?',
    answer: 'FXentra is a professional trading platform that provides traders access to significant trading capital of up to $200,000. We offer a merit-based system where skilled traders can prove their abilities and trade with our capital while keeping up to 90% of the profits they generate.'
  },
  {
    id: 2,
    question: 'What trading platforms do you support?',
    answer: 'We support multiple popular trading platforms including MetaTrader 4, MetaTrader 5, and cTrader. You can choose the platform you\'re most comfortable with to trade your account.'
  },
  {
    id: 3,
    question: 'What are the risk management rules?',
    answer: 'Our risk management rules vary by account type. For Instant accounts, the maximum daily drawdown is 2.5% with an overall limit of 5%. For One-Step accounts, it\'s 4% daily and 8% overall. For Two-Step accounts, it\'s 4% daily and 10% overall. All accounts use equity-based drawdown calculations.'
  },
  {
    id: 4,
    question: 'What are the trading objectives?',
    answer: 'Trading objectives vary by account type. Instant accounts have no profit targets. One-Step accounts have a 10% profit target. Two-Step accounts have a 10% target for Phase 1 and 5% for Phase 2.'
  },
  {
    id: 5,
    question: 'How does the profit sharing work?',
    answer: 'Successful traders on Instant and One-Step accounts receive 90% of generated profits. Two-Step account traders receive 80% of profits. Profit withdrawals are processed bi-weekly to ensure regular payouts.'
  },
  {
    id: 6,
    question: 'When can I request my first payout for an Instant account?',
    answer: 'For Instant accounts, you must complete 14 trading days and execute a minimum of 30 trades to request your first withdrawal. This requirement ensures that traders demonstrate consistent performance before accessing profits.'
  },
  {
    id: 7,
    question: 'What payment methods do you accept?',
    answer: 'We currently only accept cryptocurrency payments including USDT (BEP20), BNB, BTC, and ETH. Cryptocurrency payments offer faster processing times and reduced fees compared to traditional payment methods. This also applies to profit payouts.'
  },
  {
    id: 8,
    question: 'What markets can I trade?',
    answer: 'We offer access to a wide range of markets including Forex, Indices, Commodities, and Cryptocurrencies. You can use your preferred trading style and strategy as long as you adhere to our risk management guidelines.'
  },
  {
    id: 9,
    question: 'What happens if I violate a trading rule?',
    answer: 'Violating a trading rule typically results in account termination, as our rules are designed to ensure responsible trading. However, we offer rule violation protection that may apply in certain circumstances. Please review our detailed trading rules for specific information.'
  },
  {
    id: 10,
    question: 'Is news trading allowed?',
    answer: 'News trading is not allowed for Instant accounts but is permitted for One-Step and Two-Step accounts. This policy helps maintain stability for our instantly funded accounts while providing more flexibility for evaluation accounts.'
  },
  {
    id: 11,
    question: 'What is the consistency rule?',
    answer: 'The consistency rule ensures traders maintain a balanced approach to risk. For Instant accounts, a 30% consistency rule applies, while One-Step accounts have a 25% consistency rule. Two-Step accounts do not have a consistency rule requirement.'
  },
  {
    id: 12,
    question: 'Can I hold trades over the weekend?',
    answer: 'Weekend holding is not allowed on any account type. All positions must be closed before the weekend market closure to prevent exposure to weekend gap risk and volatility.'
  },
  {
    id: 13,
    question: 'How quickly will I receive my funded account?',
    answer: 'Instant accounts provide immediate access after payment confirmation. One-Step accounts require completion of a single evaluation phase, while Two-Step accounts require completion of two evaluation phases before receiving a funded account.'
  },
  {
    id: 14,
    question: 'How can I scale my account size?',
    answer: 'We offer a scaling plan that allows successful traders to increase their account size. After demonstrating consistent profitability and responsible risk management, you can be eligible for account upgrades up to our maximum capital allocation.'
  }
];

export default function FAQsPage() {
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  const toggleFaq = (id: number) => {
    setOpenFaq(openFaq === id ? null : id);
  };

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const categories = {
    'Platform': [1, 2, 8],
    'Trading Rules': [3, 4, 9, 10, 11, 12],
    'Account & Payments': [5, 6, 7, 13, 14]
  };

  return (
    <div className="min-h-screen bg-[#0A1118]">
      {/* Professional Header Section */}
      <div className="relative bg-[#0F1A2E] border-b border-teal-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div className="inline-block px-4 py-1.5 rounded-full bg-gradient-to-r from-teal-500/10 to-blue-500/10 backdrop-blur-sm border border-teal-500/20 mb-6">
              <span className="text-teal-400 font-medium text-sm uppercase tracking-wider">Knowledge Base</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300 mt-4 mb-6">
              Frequently Asked Questions
            </h1>
            <div className="h-1 w-24 mx-auto bg-gradient-to-r from-teal-500 to-teal-300 rounded-full mb-8"></div>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Find comprehensive answers to common questions about our trading platform, account management, and trading rules.
            </p>

            {/* Search Bar */}
            <div className="mt-10 max-w-2xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search for answers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-6 py-4 bg-gray-900/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-teal-500/40 transition-all duration-300"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'40\' height=\'40\' viewBox=\'0 0 40 40\'%3E%3Cg fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zm33.66 30.1l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zm-14.66 0l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zM1.41 22l2.83-2.83 1.41 1.41L2.83 23.41 1.41 22zm32.25-18.24l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zm-14.66 0l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zM1.41 4.24l2.83-2.83 1.41 1.41L2.83 5.65 1.41 4.24z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
              backgroundSize: '24px 24px'
            }}
          />
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[1000px] h-[1000px] rounded-full bg-teal-500/5 blur-[150px] pointer-events-none" />
          <div className="absolute top-40 left-10 w-80 h-80 bg-teal-400/5 rounded-full blur-[100px]" />
          <div className="absolute bottom-40 right-10 w-96 h-96 bg-blue-400/5 rounded-full blur-[120px]" />
          <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-teal-300 rounded-full shadow-[0_0_15px_5px_rgba(45,212,191,0.3)]" />
          <div className="absolute bottom-1/3 left-1/4 w-3 h-3 bg-teal-300 rounded-full shadow-[0_0_20px_6px_rgba(45,212,191,0.25)]" />
        </div>
      </div>

      {/* FAQs Content Section with Categories */}
      <div className="container max-w-7xl mx-auto px-4 py-20 relative z-10">
        {Object.entries(categories).map(([category, faqIds], categoryIndex) => (
          <div key={category} className={`mb-16 ${categoryIndex > 0 ? 'mt-20' : ''}`}>
            <div className="mb-10">
              <h2 className="text-2xl font-semibold text-white mb-4">{category}</h2>
              <div className="h-0.5 w-16 bg-gradient-to-r from-teal-500 to-teal-300 rounded-full"></div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {filteredFaqs
                .filter(faq => faqIds.includes(faq.id))
                .map((faq, index) => (
                  <div 
                    key={faq.id} 
                    className={`relative group ${
                      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                    }`}
                    style={{ 
                      transitionDelay: `${index * 50}ms`,
                      transition: 'all 0.6s cubic-bezier(0.25, 1, 0.5, 1)'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-blue-500/5 rounded-2xl transform transition-all duration-300 group-hover:scale-[1.02] group-hover:from-teal-500/10 group-hover:to-blue-500/10"></div>
                    <div className="absolute inset-0 border border-gray-800/50 rounded-2xl group-hover:border-teal-500/30 transition-colors duration-300"></div>
                    <div className="absolute top-0 left-[5%] right-[5%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
                    <div className="absolute bottom-0 left-[5%] right-[5%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/10 to-transparent"></div>
                    
                    <div className="relative p-8 rounded-2xl backdrop-blur-sm bg-gray-900/30">
                      <button
                        className="flex justify-between items-start w-full text-left focus:outline-none group"
                        onClick={() => toggleFaq(faq.id)}
                        aria-expanded={openFaq === faq.id}
                      >
                        <span className="text-lg font-medium text-white group-hover:text-teal-300 transition-colors duration-300 pr-8">{faq.question}</span>
                        <div className={`ml-4 shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
                          openFaq === faq.id ? 'bg-teal-500/20 text-teal-400 rotate-180' : 'bg-gray-800/50 text-gray-400 group-hover:bg-teal-500/10 group-hover:text-teal-300'
                        }`}>
                          <svg
                            className="w-5 h-5 transform transition-transform duration-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </button>
                      <div 
                        className={`transition-all duration-500 ease-in-out overflow-hidden ${
                          openFaq === faq.id ? 'max-h-96 opacity-100 mt-6' : 'max-h-0 opacity-0'
                        }`}
                      >
                        <div className="py-2 pl-0 pr-8 border-t border-gray-800/30">
                          <p className="text-gray-400 leading-relaxed pt-4">{faq.answer}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        ))}
        
        {/* Enhanced Contact Section */}
        <div className="mt-24 text-center relative">
          <div className="absolute inset-0 bg-gradient-to-r from-teal-500/5 via-blue-500/5 to-teal-500/5 rounded-2xl -z-10"></div>
          <div className="py-16 px-8 rounded-2xl border border-gray-800/40 backdrop-blur-sm">
            <div className="inline-block p-3 rounded-full bg-teal-500/10 mb-6">
              <svg className="w-8 h-8 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-3xl font-semibold text-white mb-4">Still Have Questions?</h3>
            <p className="text-gray-400 mb-8 max-w-2xl mx-auto text-lg">
              Our dedicated support team is available 24/7 to assist you with any questions about our trading platform.
            </p>
            <div className="flex justify-center gap-4">
              <a 
                href="#contact" 
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal-500 to-teal-400 text-white font-medium rounded-xl transition-all duration-300 hover:shadow-[0_0_20px_rgba(20,184,166,0.4)] hover:scale-105"
              >
                Contact Support
                <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
              <a 
                href="#live-chat" 
                className="inline-flex items-center px-8 py-4 bg-gray-800/50 text-white font-medium rounded-xl border border-gray-700 transition-all duration-300 hover:bg-gray-800 hover:border-teal-500/30"
              >
                Live Chat
                <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 
