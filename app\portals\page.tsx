'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FIRM_CONFIG, FirmType } from '@/contexts/FirmContext';

export default function PortalsPage() {
  const [hoveredFirm, setHoveredFirm] = useState<FirmType | null>(null);

  const firms: { key: FirmType; route: string }[] = [
    { key: 'horizon', route: '/horizon-admin' },
    { key: 'whales', route: '/whales-admin' },
    { key: 'entra', route: '/entra-admin' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#050A10] via-[#0A0E17] to-[#131B29] relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Animated geometric shapes */}
        <div className="geometric-shape shape-teal-glow w-[800px] h-[800px] -top-[400px] -left-[400px] opacity-[0.03]"></div>
        <div className="geometric-shape shape-orange-glow w-[600px] h-[600px] -bottom-[200px] -right-[200px] opacity-[0.03]"></div>
        <div className="geometric-shape shape-blue-glow w-[500px] h-[500px] top-[30%] -right-[250px] opacity-[0.03]"></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-[0.02]"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Admin <span className="bg-gradient-to-r from-teal-400 via-blue-400 to-orange-400 bg-clip-text text-transparent">Portals</span>
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed">
            Select your trading firm to access the admin dashboard with dedicated backend services and customized interface
          </p>
        </div>

        {/* Firm Selection Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl w-full">
          {firms.map(({ key, route }) => {
            const config = FIRM_CONFIG[key];
            const isHovered = hoveredFirm === key;
            
            return (
              <Link
                key={key}
                href={route}
                className="group relative"
                onMouseEnter={() => setHoveredFirm(key)}
                onMouseLeave={() => setHoveredFirm(null)}
              >
                <div className={`
                  relative overflow-hidden rounded-2xl p-8 transition-all duration-500 transform
                  ${isHovered ? 'scale-105 -translate-y-2' : 'scale-100'}
                  bg-gradient-to-br from-[#0F1A2E]/90 to-[#1E293B]/90 backdrop-blur-xl
                  border-2 transition-colors duration-300
                  ${key === 'horizon' ? 'border-orange-600/20 hover:border-orange-600/60' : ''}
                  ${key === 'whales' ? 'border-blue-600/20 hover:border-blue-600/60' : ''}
                  ${key === 'entra' ? 'border-teal-500/20 hover:border-teal-500/60' : ''}
                  hover:shadow-2xl
                `}>
                  {/* Glow effect */}
                  <div className={`
                    absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500
                    ${key === 'horizon' ? 'bg-gradient-to-br from-orange-600/30 to-orange-400/10' : ''}
                    ${key === 'whales' ? 'bg-gradient-to-br from-blue-600/30 to-blue-400/10' : ''}
                    ${key === 'entra' ? 'bg-gradient-to-br from-teal-500/30 to-teal-400/10' : ''}
                  `}></div>

                  {/* Content */}
                  <div className="relative z-10 text-center">
                    {/* Logo/Icon */}
                    <div className={`
                      w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center
                      transition-all duration-300 group-hover:scale-110
                      ${key === 'horizon' ? 'bg-gradient-to-br from-orange-600 to-orange-500' : ''}
                      ${key === 'whales' ? 'bg-gradient-to-br from-blue-600 to-blue-500' : ''}
                      ${key === 'entra' ? 'bg-gradient-to-br from-teal-500 to-teal-400' : ''}
                    `}>
                      {key === 'horizon' && (
                        <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      )}
                      {key === 'whales' && (
                        <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      )}
                      {key === 'entra' && (
                        <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                      )}
                    </div>

                    {/* Firm Name */}
                    <h3 className="text-3xl font-bold text-white mb-3">
                      {config.displayName}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-400 mb-6 leading-relaxed">
                      Access {config.displayName} admin dashboard with dedicated backend services and real-time data management
                    </p>

                    {/* Action Button */}
                    <div className={`
                      inline-flex items-center px-6 py-3 rounded-full font-medium transition-all duration-300
                      ${key === 'horizon' ? 'bg-orange-600 hover:bg-orange-700 text-white' : ''}
                      ${key === 'whales' ? 'bg-blue-600 hover:bg-blue-700 text-white' : ''}
                      ${key === 'entra' ? 'bg-teal-600 hover:bg-teal-700 text-white' : ''}
                      group-hover:scale-105 group-hover:shadow-lg
                    `}>
                      <span>Access Portal</span>
                      <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </div>
                  </div>

                  {/* Corner decoration */}
                  <div className={`
                    absolute top-4 right-4 w-2 h-2 rounded-full transition-all duration-300
                    ${key === 'horizon' ? 'bg-orange-400' : ''}
                    ${key === 'whales' ? 'bg-blue-400' : ''}
                    ${key === 'entra' ? 'bg-teal-400' : ''}
                    ${isHovered ? 'scale-150 opacity-100' : 'scale-100 opacity-60'}
                  `}></div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Footer Info */}
        <div className="mt-16 text-center">
          <p className="text-gray-500 text-sm">
            Each portal connects to its dedicated backend service with independent data management
          </p>
        </div>
      </div>
    </div>
  );
}
